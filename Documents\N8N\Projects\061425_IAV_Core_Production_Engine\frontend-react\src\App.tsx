import React, { useState } from 'react';
import { Plus, Activity, Target, Zap } from 'lucide-react';
import './index.css';

// Simple Job interface
interface Job {
  id: string;
  title: string;
  status: 'active' | 'completed' | 'failed' | 'pending';
  progress_percent: number;
  total_clips: number;
  created_at: string;
}

// Mock data
const mockJobs: Job[] = [
  {
    id: '1',
    title: 'Rick Roll Viral Clips',
    status: 'completed',
    progress_percent: 100,
    total_clips: 5,
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Tech Review Highlights',
    status: 'active',
    progress_percent: 65,
    total_clips: 3,
    created_at: new Date().toISOString()
  }
];

function App() {
  const [jobs] = useState<Job[]>(mockJobs);

  // Computed Values
  const activeJobs = jobs.filter(j => j.status === 'active');
  const completedJobs = jobs.filter(j => j.status === 'completed');
  const failedJobs = jobs.filter(j => j.status === 'failed');

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              💼 Jobs Control Center
            </h1>
            <p className="text-muted-foreground mt-1">
              Executive command center for viral content operations
            </p>
          </div>
          <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold px-6 py-3 rounded-md hover:from-blue-700 hover:to-purple-700 hover:scale-105 hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <span>Create Job</span>
          </button>
        </div>

        {/* Job Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="metric-card">
            <div className="metric-value">{activeJobs.length}</div>
            <div className="metric-label flex items-center">
              <Activity className="w-4 h-4 mr-1" />
              Active Jobs
            </div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{completedJobs.length}</div>
            <div className="metric-label flex items-center">
              <Target className="w-4 h-4 mr-1" />
              Completed
            </div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{failedJobs.length}</div>
            <div className="metric-label flex items-center">
              <Zap className="w-4 h-4 mr-1 text-red-400" />
              Failed
            </div>
          </div>
          <div className="metric-card">
            <div className="metric-value">2/4</div>
            <div className="metric-label">Platforms Online</div>
          </div>
        </div>

        {/* Job Display */}
        {jobs.length === 0 ? (
          <div className="glass-panel p-8 text-center">
            <div className="text-6xl mb-6">🚀</div>
            <h3 className="text-2xl font-semibold mb-3">Ready for Launch</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Your executive command center is ready. Launch your first viral content operation to begin generating revenue.
            </p>
          </div>
        ) : (
          <div>
            <h2 className="text-xl font-semibold mb-4">Job Queue</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {jobs.map((job) => (
                <div key={job.id} className="job-card">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg truncate">{job.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      job.status === 'active' ? 'bg-blue-500/20 text-blue-400' :
                      job.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                      job.status === 'failed' ? 'bg-red-500/20 text-red-400' :
                      'bg-gray-500/20 text-gray-400'
                    }`}>
                      {job.status}
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Progress:</span>
                      <span className="font-medium">{job.progress_percent}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${job.progress_percent}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Clips:</span>
                      <span className="font-medium">{job.total_clips}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
